# Debug.py
import requests
import time
import json
import re # Added for prompt cleaning
from requests.exceptions import Timeout, ConnectionError, RequestException

# --- TavilySearchAPI Class (Keep as is) ---
class TavilySearchAPI:
    def __init__(self, api_key):
        self.api_key = api_key
        self.api_url = "https://api.tavily.com/search"

    def search(self, query, max_results=15, content_length=800, num_results=8):
        result = self._send_search_request(query, max_results)
        if not result:
            return {"success": False, "error": "搜索请求失败"}
        context = self._prepare_context(result, num_results, content_length)
        return {
            "success": True,
            "query": query,
            "answer": result.get("answer", "无自动生成答案"),
            "context": context,
            "results_count": len(result.get("results", []))
        }

    def _send_search_request(self, query, max_results):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        payload = {
            "query": query, "search_depth": "advanced", "include_answer": True,
            "include_raw_content": True, "include_images": False, "max_results": max_results
        }
        try:
            # Use global request timeout defined later
            response = requests.post(self.api_url, headers=headers, json=payload, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"搜索请求出错: {e}")
            return None

    def _prepare_context(self, result, num_results, content_length):
        context = []
        if result.get("answer"):
            context.append(f"搜索自动生成的答案: {result['answer']}")
        results = result.get("results", [])
        if results:
            context.append("\n搜索结果内容:")
            for i, item in enumerate(results[:num_results], 1):
                title = item.get("title", f"结果 {i}")
                url = item.get("url", "无链接")
                content = item.get("content", "无内容").replace("\n", " ")[:content_length]
                context.append(f"\n[{i}] {title}\nURL: {url}\n内容: {content}...")
        return "\n\n".join(context)

# --- Main Application Logic ---

# 加载配置文件
try:
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
except FileNotFoundError:
    print("错误: config.json 未找到。请确保配置文件存在。")
    exit()
except json.JSONDecodeError:
    print("错误: config.json 格式无效。请检查 JSON 语法。")
    exit()

# 从配置加载常量
API_BASE_URL = config["API_BASE_URL"]
LLM_API_URL = config["LLM_API_URL"]
API_KEY = config["API_KEY"]
SEARCH_API_KEY = config["SEARCH_API_KEY"]
# --- Load Seedream Config ---
SEEDREAM_API_KEY = config.get("SEEDREAM_API_KEY", None) # Use .get for safety
SEEDREAM_BASE_URL = config.get("SEEDREAM_BASE_URL", "https://www.dmxapi.cn") # Default if missing
# --- End Seedream Config ---
REQUEST_TIMEOUT = config["REQUEST_TIMEOUT"]
TARGET_GROUP_ID = config["TARGET_GROUP_ID"]
BOT_ID = config["BOT_ID"]
CHECK_INTERVAL = config["CHECK_INTERVAL"]
ENABLE_INTERNET_SEARCH = config["ENABLE_INTERNET_SEARCH"]
ENABLE_IMAGE_GENERATION = config["ENABLE_IMAGE_GENERATION"]
ENABLE_WHITELIST_CHECK = config["ENABLE_WHITELIST_CHECK"]
TEXT_MODELS = config["TEXT_MODELS"]
IMAGE_MODELS = config["IMAGE_MODELS"]
CURRENT_TEXT_MODEL = config["CURRENT_TEXT_MODEL"]
CURRENT_IMAGE_MODEL = config["CURRENT_IMAGE_MODEL"]
WHITELIST_USERS = config["WHITELIST_USERS"]
ADMIN_USERS = config["ADMIN_USERS"] # Load admin users

# --- Seedream Image Generation Constants ---
SEEDREAM_API_ENDPOINT = "/v1/images/generations" # From seedream.py
DEFAULT_IMAGE_SIZE = "1024x1024" # Or load from config if needed

# 状态变量
is_stopped = False

def save_config():
    """保存配置到 JSON 文件"""
    global config, CURRENT_TEXT_MODEL, CURRENT_IMAGE_MODEL, ENABLE_INTERNET_SEARCH, ENABLE_IMAGE_GENERATION, ENABLE_WHITELIST_CHECK
    config["CURRENT_TEXT_MODEL"] = CURRENT_TEXT_MODEL
    config["CURRENT_IMAGE_MODEL"] = CURRENT_IMAGE_MODEL
    # Ensure all current settings are reflected in the config object before saving
    config["ENABLE_INTERNET_SEARCH"] = ENABLE_INTERNET_SEARCH
    config["ENABLE_IMAGE_GENERATION"] = ENABLE_IMAGE_GENERATION
    config["ENABLE_WHITELIST_CHECK"] = ENABLE_WHITELIST_CHECK
    # ... add any other potentially runtime-modified settings if needed

    try:
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
    except IOError as e:
        print(f"错误: 无法写入 config.json: {e}")


def fetch_group_messages(group_id: int, limit: int = 15) -> list:
    """获取群聊历史消息并返回消息列表"""
    try:
        response = requests.get(f"{API_BASE_URL}/get_group_msg_history",
                              params={"group_id": group_id, "num": limit},
                              timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        data = response.json()
        # Add basic check for expected response structure from go-cqhttp
        if data.get("retcode") == 0 and "data" in data and "messages" in data["data"]:
             return data["data"]["messages"]
        else:
            print(f"获取群消息API返回格式异常: {data}")
            return []
    except RequestException as e:
        print(f"获取群消息失败: {e}")
        return []

def check_mention(messages: list, bot_id: int, last_processed_time: int = 0, last_processed_msg_id: int = -1) -> tuple:
    """
    检查消息列表中是否存在@机器人的新消息(新于last_processed)，并返回第一条匹配的信息。
    返回: (matched_text, sender_id, matched_msg_time, matched_msg_id, latest_time_in_batch, latest_id_in_batch)
    """
    matched_text = None
    sender_id = None
    matched_msg_time = 0
    matched_msg_id = -1

    # Sort messages by time (ascending) then ID (ascending) to process in order
    # Then reverse to check newest first efficiently
    # Filter out potential None messages first
    valid_messages = [m for m in messages if isinstance(m, dict)]
    if not valid_messages:
         # Find the absolute latest message time/id from the original (possibly empty) batch
        latest_time = last_processed_time
        latest_msg_id = last_processed_msg_id
        return None, None, 0, -1, latest_time, latest_msg_id

    sorted_messages = sorted(valid_messages, key=lambda x: (x.get("time", 0), x.get("message_id", -1)))
    sorted_messages.reverse() # Newest first

    # Find the *first* new message (newest overall) that is an @mention
    for msg in sorted_messages:
        msg_time = msg.get("time", 0)
        msg_id = msg.get("message_id", -1)

        # Skip already processed messages based on BOTH time and ID
        if msg_time < last_processed_time or (msg_time == last_processed_time and msg_id <= last_processed_msg_id):
            continue # Skip this older/processed message

        message_segments = msg.get("message", [])
        if not isinstance(message_segments, list): # Handle potential non-list message field
            continue

        is_mention = False
        for seg in message_segments:
             if isinstance(seg, dict) and seg.get("type") == "at" and seg.get("data", {}).get("qq") == str(bot_id):
                 is_mention = True
                 break

        if is_mention:
            text_content = ""
            for seg in message_segments:
                if isinstance(seg, dict) and seg.get("type") == "text":
                       text_content += seg.get("data", {}).get("text", "").strip()
            text_content = text_content.strip() # Final strip

            # Only consider it a match if there's actual text after the mention
            if text_content:
                matched_text = text_content
                sender_info = msg.get("sender", {})
                # Ensure sender_id is stored as int
                sender_id_str = sender_info.get("user_id") if isinstance(sender_info, dict) else None
                try:
                    sender_id = int(sender_id_str) if sender_id_str is not None else None
                except (ValueError, TypeError):
                    sender_id = None # Handle cases where user_id is not a valid integer string
                matched_msg_time = msg_time
                matched_msg_id = msg_id
                break # Found the newest unprocessed mention

    # Find the absolute latest message time/id from the fetched batch for updating the state
    latest_time = 0
    latest_msg_id = -1
    if valid_messages: # Use the filtered list
       latest_message = max(valid_messages, key=lambda x: (x.get("time", 0), x.get("message_id", -1)))
       latest_time = latest_message.get("time", 0)
       latest_msg_id = latest_message.get("message_id", -1)

    # Return the latest message info regardless of mention, to update the processed state correctly
    # Ensure we don't go backwards in time/ID
    final_latest_time = max(last_processed_time, latest_time)
    if final_latest_time > last_processed_time:
        final_latest_msg_id = latest_msg_id # If time moved forward, ID starts from the newest message's ID
    else: # Time is the same, only update ID if the new one is greater
        final_latest_msg_id = max(last_processed_msg_id, latest_msg_id)


    return matched_text, sender_id, matched_msg_time, matched_msg_id, final_latest_time, final_latest_msg_id


def call_llm(prompt: str, search_results: str = None, model=None) -> str:
    """调用大模型API并返回结果"""
    current_model = model if model else CURRENT_TEXT_MODEL
    print(f"正在调用大模型 ({current_model}): {LLM_API_URL}")
    try:
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {API_KEY}"
        }
        messages_payload = [{"role": "user", "content": prompt}]
        if search_results:
            messages_payload.insert(0, {
                "role": "system",
                "content": f"以下是互联网搜索结果，可用于回答用户问题：\n{search_results}"
            })
        data = {
            "model": current_model, "messages": messages_payload,
            "temperature": 0.7, "max_tokens": 1500 # Increased max_tokens slightly
        }
        response = requests.post(LLM_API_URL, headers=headers, json=data, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        result = response.json()
        content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
        if not content:
             print(f"大模型响应为空或格式错误: {result}")
             return "error_empty_response"
        print("大模型响应成功")
        return content.strip()

    except Timeout:
        print(f"大模型请求超时（{REQUEST_TIMEOUT}秒）")
        return "error_timeout"
    except ConnectionError:
        print("无法连接到大模型API")
        return "error_connection"
    except RequestException as e:
        print(f"大模型API请求失败: {e}")
        try:
            error_details = response.json(); print(f"详细错误: {json.dumps(error_details, indent=2)}")
        except Exception:
            # If accessing response fails (e.g., connection error), ignore
            pass
        return "error_api"
    except Exception as e:
        print(f"调用大模型发生未知错误: {e}")
        return "error_unknown"

# --- Seedream Image Generation Function ---
def call_seedream_api(prompt: str, model: str = CURRENT_IMAGE_MODEL, size: str = DEFAULT_IMAGE_SIZE) -> str | None:
    """
    Generates a single image using the Seedream (DMX) API based on the provided prompt.

    Args:
        prompt: The text prompt.
        model: The model name to use (e.g., "seedream-3.0").
        size: The desired image size (e.g., "1024x1024").

    Returns:
        The URL of the generated image, or None if an error occurred.
    """
    if not SEEDREAM_API_KEY:
        print("错误: Seedream API Key (SEEDREAM_API_KEY) 未在 config.json 中配置。")
        return None
    if not model:
        print("错误: 未指定有效的生图模型 (CURRENT_IMAGE_MODEL)。")
        return None

    # Construct the full API URL
    # Make sure SEEDREAM_BASE_URL doesn't have a trailing slash if SEEDREAM_API_ENDPOINT starts with one
    api_url = f"{SEEDREAM_BASE_URL.rstrip('/')}{SEEDREAM_API_ENDPOINT}"

    headers = {
        "Authorization": f"Bearer {SEEDREAM_API_KEY}",
        "Accept": "application/json",
        "User-Agent": f"QQBot/{BOT_ID}", # Identify your bot
        "Content-Type": "application/json",
    }
    payload = json.dumps(
        {
            "prompt": prompt,
            "n": 1, # Request exactly one image
            "model": model, # Use the model passed to the function
            "size": size,
        }
    )

    print(f"请求 Seedream ({model}) 生成图片: '{prompt[:50]}...'")
    try:
        # Use a potentially longer timeout for image generation specifically
        # Allow overriding via config later if needed, default to 120s
        image_timeout = config.get("IMAGE_REQUEST_TIMEOUT", 120)
        print(f"Seedream API 请求超时设置为: {image_timeout} 秒")

        response = requests.post(api_url, data=payload, headers=headers, timeout=image_timeout)
        response.raise_for_status() # Check for HTTP errors (like 4xx or 5xx)

        response_data = response.json() # Use .json() directly

        # Check if the response contains the expected data structure (based on seedream.py)
        if "data" in response_data and isinstance(response_data["data"], list) and len(response_data["data"]) > 0:
            image_info = response_data["data"][0]
            if "url" in image_info:
                image_url = image_info["url"].replace("\\u0026", "&") # Handle unicode escapes if necessary
                print(f"成功生成图片 URL: {image_url}")
                return image_url
            else:
                print(f"错误: Seedream 响应中未找到 'url' 键: {response_data}")
                return None
        else:
            # Log specific error messages from the API if available
            error_msg = response_data.get("message", "未知API错误")
            print(f"错误: Seedream 响应格式不符合预期或API返回错误: {error_msg} | 完整响应: {response_data}")
            return None

    except Timeout:
        print(f"错误: Seedream 图片生成请求超时 ({image_timeout} 秒) for prompt: '{prompt[:50]}...'")
        return None
    except RequestException as e:
        # Handles connection errors, HTTP errors, etc.
        print(f"错误: Seedream API 请求失败: {e}")
        # Log more details if possible
        try:
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}") # Log raw response text for debugging
        except Exception: # If response object doesn't exist or response.text fails
             pass
        return None
    except json.JSONDecodeError:
        print(f"错误: 无法解码 Seedream API 的 JSON 响应。 响应: {response.text}")
        return None
    except Exception as e:
        # Catch any other unexpected errors
        print(f"错误: 图片生成过程中发生未知错误: {e}")
        return None
# --- End Seedream ---

def send_group_message(group_id: int, message: str) -> dict:
    """发送群消息并返回响应结果"""
    try:
        # Limit log length for potentially long base64 images in CQ codes (though we use URL here)
        log_message = message if len(message) < 200 else message[:200] + "..."
        print(f"准备发送的消息 (Group {group_id}): {log_message}")

        response = requests.post(f"{API_BASE_URL}/send_group_msg",
                               json={"group_id": group_id, "message": message},
                               timeout=REQUEST_TIMEOUT) # Use standard timeout for sending
        response.raise_for_status()

        result = response.json()
        # Log less verbosely on success
        if result.get("status") == "ok" or result.get("retcode") == 0:
             print(f"消息发送成功 (Group {group_id}), Message ID: {result.get('data', {}).get('message_id', 'N/A')}")
        else:
             print(f"发送消息API返回异常状态: {result}")
        return result
    except RequestException as e:
        print(f"发送消息失败 (Group {group_id}): {e}")
        # Try to include response text if available
        error_details = ""
        try:
            error_details = response.text
        except Exception:
            pass
        print(f"发送失败详情: {error_details}")
        return {"status": "error", "retcode": -1, "message": str(e)}


def need_internet_search(prompt: str) -> bool:
    """判断问题是否需要联网搜索"""
    if not ENABLE_INTERNET_SEARCH:
        return False
    # Use a cheaper/faster model for judgment if possible, or stick to config
    # Let's keep using doubao-pro for consistency unless a specific judge model is set
    judge_model = config.get("JUDGE_MODEL", "doubao-pro-32k")
    print(f"使用模型 {judge_model} 判断是否需要联网: '{prompt[:50]}...'")

    # Slightly refined prompt for clarity
    judge_prompt = f"""分析以下用户问题，判断回答该问题是否**必须**依赖实时、具体的最新信息（例如今天的天气、特定股票的当前价格、刚发生的事件详情）。如果问题是关于一般知识、概念解释、历史事件、虚构内容或可以通过模型已有知识库推断的内容，则不需要联网。

请仅严格回答"需要搜索"或"不需要搜索"。

问题："{prompt}" """

    response = call_llm(judge_prompt, model=judge_model) # Use specific model here
    print(f"联网判断结果: {response}")
    # More robust check, handles potential variations like "需要搜索。"
    return "需要搜索" in response and "不需要搜索" not in response

def perform_search(query: str) -> str | None: # Return type hint added
    """执行搜索并返回结果"""
    if not SEARCH_API_KEY:
        print("警告: 未配置 SEARCH_API_KEY，无法执行联网搜索。")
        return None
    try:
        print(f"正在执行 Tavily 搜索: '{query[:50]}...'")
        search_api = TavilySearchAPI(api_key=SEARCH_API_KEY)
        result = search_api.search(query)

        if result["success"] and result["context"]:
            print("Tavily 搜索成功，获得结果。")
            return result["context"]
        else:
            print(f"Tavily 搜索失败或无结果: {result.get('error', '未知错误')}")
            return None
    except Exception as e:
        print(f"执行 Tavily 搜索时发生错误: {e}")
        return None

# MODIFICATION 1: Add sender_id parameter
def handle_model_commands(sender_id: int, prompt: str) -> str | None:
    """处理模型切换和列出命令 (切换仅限管理员)"""
    global CURRENT_TEXT_MODEL, CURRENT_IMAGE_MODEL
    prompt_normalized = prompt.lower().strip() # Normalize command

    if prompt_normalized == "/get_chat":
        # Listing models is allowed for everyone
        return f"当前文本模型: {CURRENT_TEXT_MODEL}\n可用文本模型: {', '.join(TEXT_MODELS)}"
    elif prompt_normalized == "/get_image":
        # Listing models is allowed for everyone
        return f"当前生图模型: {CURRENT_IMAGE_MODEL}\n可用生图模型: {', '.join(IMAGE_MODELS)}"
    elif prompt_normalized.startswith("/to "):
        # --- MODIFICATION 2: Add Admin Check ---
        if sender_id not in ADMIN_USERS:
            return "🚫 您没有权限切换模型。" # Permission denied
        # --- End Admin Check ---

        # If admin, proceed with switching logic
        target_model = prompt[4:].strip() # Keep original case for matching keys
        target_model_lower = target_model.lower()

        # Check text models (case-insensitive)
        matched_text_model = None
        for m in TEXT_MODELS:
            if target_model_lower == m.lower():
                matched_text_model = m
                break
        if matched_text_model:
            CURRENT_TEXT_MODEL = matched_text_model
            save_config()
            print(f"ADMIN COMMAND: Text model switched to {CURRENT_TEXT_MODEL} by {sender_id}") # Log admin action
            return f"✅ 已切换到文本模型: {CURRENT_TEXT_MODEL}"

        # Check image models (case-insensitive)
        matched_image_model = None
        for m in IMAGE_MODELS:
             if target_model_lower == m.lower():
                 matched_image_model = m
                 break
        if matched_image_model:
             CURRENT_IMAGE_MODEL = matched_image_model
             save_config()
             print(f"ADMIN COMMAND: Image model switched to {CURRENT_IMAGE_MODEL} by {sender_id}") # Log admin action
             return f"✅ 已切换到生图模型: {CURRENT_IMAGE_MODEL}"

        # If no match found (and user is admin)
        return f"❌ 未找到模型: '{target_model}'。\n可用文本模型: {', '.join(TEXT_MODELS)}\n可用生图模型: {', '.join(IMAGE_MODELS)}"

    return None # Not a model command

def handle_admin_commands(sender_id: int, prompt: str) -> str | None: # Return hint added
    """处理管理员命令"""
    global is_stopped, ENABLE_INTERNET_SEARCH, ENABLE_IMAGE_GENERATION, ENABLE_WHITELIST_CHECK
    prompt_normalized = prompt.lower().strip() # Normalize

    if sender_id in ADMIN_USERS:
        if prompt_normalized == "stop":
            if is_stopped:
                 return "ℹ️ 机器人已经处于暂停状态。"
            is_stopped = True
            print(f"ADMIN COMMAND: Bot stopped by {sender_id}")
            return "⏸️ 已暂停接收新消息处理。"
        elif prompt_normalized == "start":
            if not is_stopped:
                return "ℹ️ 机器人已经在运行中。"
            is_stopped = False
            print(f"ADMIN COMMAND: Bot started by {sender_id}")
            return "▶️ 已恢复接收新消息处理。"
        elif prompt_normalized == "search on":
             ENABLE_INTERNET_SEARCH = True
             save_config()
             print(f"ADMIN COMMAND: Internet search enabled by {sender_id}")
             return "✅ 已启用联网搜索。"
        elif prompt_normalized == "search off":
             ENABLE_INTERNET_SEARCH = False
             save_config()
             print(f"ADMIN COMMAND: Internet search disabled by {sender_id}")
             return "❌ 已禁用联网搜索。"
        elif prompt_normalized == "image on":
             ENABLE_IMAGE_GENERATION = True
             save_config()
             print(f"ADMIN COMMAND: Image generation enabled by {sender_id}")
             return "✅ 已启人生图功能。"
        elif prompt_normalized == "image off":
            ENABLE_IMAGE_GENERATION = False
            save_config()
            print(f"ADMIN COMMAND: Image generation disabled by {sender_id}")
            return "❌ 已禁用生图功能。"
        elif prompt_normalized == "whitelist on":
             ENABLE_WHITELIST_CHECK = True
             save_config()
             print(f"ADMIN COMMAND: Whitelist check enabled by {sender_id}")
             return "✅ 已启用白名单检查。"
        elif prompt_normalized == "whitelist off":
            ENABLE_WHITELIST_CHECK = False
            save_config()
            print(f"ADMIN COMMAND: Whitelist check disabled by {sender_id}")
            return "❌ 已禁用白名单检查。"
         # Add other admin commands here if needed

    # If command is recognized admin command but user is not admin
    elif prompt_normalized in ["stop", "start", "search on", "search off", "image on", "image off", "whitelist on", "whitelist off"]:
         # Check if it *looks* like an admin command but wasn't handled above means user is not admin
         return "🚫 您没有权限执行此命令。"

    return None # Not an admin command or not recognized


if __name__ == "__main__":
    last_processed_time = 0
    last_processed_msg_id = -1
    first_run_completed = False

    print("--- Bot Initialization ---")
    print(f"监控群组: {TARGET_GROUP_ID}")
    print(f"机器人ID: {BOT_ID}")
    print(f"检查间隔: {CHECK_INTERVAL}s")
    print(f"请求超时: {REQUEST_TIMEOUT}s")
    print(f"文本模型: {CURRENT_TEXT_MODEL} (可用: {', '.join(TEXT_MODELS)})")
    print(f"生图模型: {CURRENT_IMAGE_MODEL} (可用: {', '.join(IMAGE_MODELS)})")
    print(f"联网搜索: {'启用' if ENABLE_INTERNET_SEARCH else '禁用'}")
    print(f"生图功能: {'启用' if ENABLE_IMAGE_GENERATION else '禁用'}")
    print(f"白名单检查: {'启用' if ENABLE_WHITELIST_CHECK else '禁用'} (白名单用户: {WHITELIST_USERS})")
    print(f"管理员: {ADMIN_USERS}") # Important: Check who the admins are
    if ENABLE_IMAGE_GENERATION and not SEEDREAM_API_KEY:
        print("\n警告: 生图功能已启用，但 SEEDREAM_API_KEY 未在 config.json 中配置！\n")
    print("-------------------------")
    print("开始运行...")

    try:
        while True:
            messages = fetch_group_messages(TARGET_GROUP_ID)

            # Pass the *current* last processed state to check_mention
            matched_text, sender_id, matched_msg_time, matched_msg_id, \
            latest_time_in_batch, latest_id_in_batch = check_mention(
                messages, BOT_ID, last_processed_time, last_processed_msg_id
            )

            # --- Initial Run Logic ---
            # Mark first run complete and update state to avoid processing old messages on startup
            if not first_run_completed:
                first_run_completed = True
                if latest_time_in_batch > last_processed_time or \
                   (latest_time_in_batch == last_processed_time and latest_id_in_batch > last_processed_msg_id):
                    last_processed_time = latest_time_in_batch
                    last_processed_msg_id = latest_id_in_batch
                    print(f"首次运行完成，记录最新消息状态: Time={last_processed_time}, ID={last_processed_msg_id}。开始处理新消息。")
                else:
                    print("首次运行完成，未发现比初始状态更新的消息。开始处理新消息。")
                time.sleep(CHECK_INTERVAL) # Wait before processing the *next* cycle
                continue # Skip processing logic on first pass

            # --- Update state BEFORE processing ---
            # Important: Update the 'seen' state *before* potentially long operations
            # This prevents reprocessing if the script crashes during LLM/Image call
            # We update to the latest message seen *in this batch*, even if it wasn't an @mention yet.
            current_latest_time_processed = last_processed_time
            current_latest_id_processed = last_processed_msg_id
            if latest_time_in_batch > last_processed_time or \
               (latest_time_in_batch == last_processed_time and latest_id_in_batch > last_processed_msg_id):
                last_processed_time = latest_time_in_batch
                last_processed_msg_id = latest_id_in_batch
                # Optional: Log state update
                # print(f"State updated BEFORE processing: T={last_processed_time}, ID={last_processed_msg_id}")


            # --- Bot Stopped State Handling ---
            if is_stopped:
                # Only process "start" command from an admin while stopped
                if matched_text and sender_id and matched_text.lower().strip() == "start" and sender_id in ADMIN_USERS:
                     print(f"\n检测到来自管理员 {sender_id} 的 'start' 命令 (机器人暂停中)")
                     admin_response = handle_admin_commands(sender_id, matched_text)
                     if admin_response:
                         # Send confirmation immediately
                         reply_message = f"[机器人]: [CQ:at,qq={sender_id}] {admin_response}"
                         send_group_message(TARGET_GROUP_ID, reply_message)
                         # Note: State was already updated above to include this message ID/Time
                     # No else needed, if it wasn't the "start" command it's ignored anyway
                # Log ignored mentions while stopped (optional)
                elif matched_text and sender_id:
                    print(f"机器人暂停中，忽略来自 {sender_id} 的消息: {matched_text[:30]}...")

                time.sleep(CHECK_INTERVAL)
                continue # Skip normal processing loop

            # --- Normal Processing ---
            message_processed_in_cycle = False # Flag to track if we handled a specific @ mention

            if matched_text and sender_id : # A new mention was found
                # Check if this specific message has already been processed by comparing against the state *before* the pre-update
                if matched_msg_time < current_latest_time_processed or \
                  (matched_msg_time == current_latest_time_processed and matched_msg_id <= current_latest_id_processed):
                    # This message was likely processed in a previous cycle but check_mention found it again transiently. Skip.
                    # print(f"Skipping already processed mention (T={matched_msg_time}, ID={matched_msg_id})")
                    pass # Just let the loop continue to sleep
                else:
                    print(f"\n检测到新 @ 消息 (Time={matched_msg_time}, ID={matched_msg_id}):")
                    print(f"  来自: {sender_id}")
                    print(f"  内容: {matched_text}")

                    # 0. Whitelist Check (if enabled)
                    if ENABLE_WHITELIST_CHECK and sender_id not in WHITELIST_USERS:
                        print(f"  用户 {sender_id} 不在白名单，忽略。")
                        message_processed_in_cycle = True # Mark as processed (ignored)
                        # State already updated at the beginning of the loop

                    else: # User is whitelisted or check is disabled
                        # 1. Admin Commands
                        admin_response = handle_admin_commands(sender_id, matched_text)
                        if admin_response:
                            print(f"  处理管理命令: {matched_text}")
                            response_to_send = f"[机器人]: [CQ:at,qq={sender_id}] {admin_response}"
                            send_group_message(TARGET_GROUP_ID, response_to_send)
                            message_processed_in_cycle = True
                            # State already updated

                        # 2. Model Commands (only if not an admin command)
                        if not message_processed_in_cycle:
                            # MODIFICATION 3: Pass sender_id here
                            model_response = handle_model_commands(sender_id, matched_text)
                            if model_response:
                                print(f"  处理模型命令: {matched_text}")
                                response_to_send = f"[机器人]: [CQ:at,qq={sender_id}] {model_response}"
                                send_group_message(TARGET_GROUP_ID, response_to_send)
                                message_processed_in_cycle = True
                                # State already updated

                        # 3. Image Generation (only if not admin/model command and enabled)
                        if not message_processed_in_cycle and ENABLE_IMAGE_GENERATION:
                            # More specific keywords, less likely to clash with normal chat
                            image_keywords = ["画图", "生图", "画一张", "生成图片"]
                            is_image_request = False
                            found_keyword = ""
                            for keyword in image_keywords:
                                if matched_text.startswith(keyword): # Check if starts with keyword
                                    is_image_request = True
                                    found_keyword = keyword
                                    break

                            if is_image_request:
                               print(f"  检测到生图请求 (关键词: '{found_keyword}')...")
                               # Extract prompt: remove keyword and leading/trailing spaces
                               image_prompt = matched_text[len(found_keyword):].strip()

                               if not image_prompt:
                                   print("  生图请求但无有效 Prompt，发送提示。")
                                   err_msg = f"[机器人]: [CQ:at,qq={sender_id}] 请提供图片描述哦，例如： @我 画图 一只可爱的猫"
                                   send_group_message(TARGET_GROUP_ID, err_msg)
                               else:
                                   # Acknowledge before starting potentially long generation
                                   ack_msg = f"[{CURRENT_IMAGE_MODEL}]: [CQ:at,qq={sender_id}] 收到！正在为您生成图片: '{image_prompt[:30]}...' 请稍候片刻~"
                                   send_group_message(TARGET_GROUP_ID, ack_msg)

                                   image_url = call_seedream_api(image_prompt, model=CURRENT_IMAGE_MODEL, size=DEFAULT_IMAGE_SIZE)

                                   if image_url:
                                       # Use CQ code for image URL
                                       # cache=0 tries to prevent QQ caching issues, subType=0 might help sometimes
                                       response_to_send = f"[{CURRENT_IMAGE_MODEL}]: [CQ:at,qq={sender_id}] 图片来啦！\n[CQ:image,file={image_url},cache=0,subType=0]"
                                       print("  图片生成成功，准备发送。")
                                       send_group_message(TARGET_GROUP_ID, response_to_send)
                                   else:
                                       # Send specific error message if generation failed
                                       print("  图片生成失败。")
                                       fail_msg = f"[{CURRENT_IMAGE_MODEL}]: [CQ:at,qq={sender_id}] 抱歉，图片生成失败了 T_T 请检查提示词或稍后再试。"
                                       send_group_message(TARGET_GROUP_ID, fail_msg)

                               message_processed_in_cycle = True
                               # State already updated

                        # 4. LLM Chat + Optional Search (Default action if no command/image matched)
                        if not message_processed_in_cycle:
                            print("  作为聊天请求处理...")
                            search_results = None
                            should_search = False
                            if ENABLE_INTERNET_SEARCH:
                                should_search = need_internet_search(matched_text) # Store result

                            if should_search: # Check stored result
                                print("  判断需要联网搜索。")
                                search_results = perform_search(matched_text)
                                if search_results:
                                    print(f"  搜索结果获取成功，长度: {len(search_results)} 字符。")
                                    # OPTIONAL: Send status update
                                    # status_msg = f"[{CURRENT_TEXT_MODEL}]: [CQ:at,qq={sender_id}] 正在结合网络信息思考..."
                                    # send_group_message(TARGET_GROUP_ID, status_msg)
                                else:
                                    print("  搜索失败或无结果，将无联网调用大模型。")
                                    # OPTIONAL: Send status update
                                    # status_msg = f"[{CURRENT_TEXT_MODEL}]: [CQ:at,qq={sender_id}] 联网搜索失败，尝试直接回答..."
                                    # send_group_message(TARGET_GROUP_ID, status_msg)
                            elif ENABLE_INTERNET_SEARCH: # Only print if search is enabled but not needed
                                print("  判断不需要联网搜索。")
                            # else: print("  联网搜索已禁用。") # Already printed at start

                            # Call LLM
                            llm_response = call_llm(matched_text, search_results, model=CURRENT_TEXT_MODEL)

                            # Send LLM Response
                            if llm_response and not llm_response.startswith("error_"):
                                reply_message = f"[{CURRENT_TEXT_MODEL}]: [CQ:at,qq={sender_id}] {llm_response}"
                                # Basic length check for QQ limit (approx 4500 bytes is safer)
                                if len(reply_message.encode('utf-8')) > 4500:
                                    # Truncate based on byte length if possible, fallback to char length
                                    try:
                                        reply_message = reply_message.encode('utf-8')[:4490].decode('utf-8', 'ignore') + "... (内容过长截断)"
                                    except Exception:
                                         reply_message = reply_message[:1500] + "... (内容过长截断)" # Fallback truncation
                                print(f"  LLM 回复准备发送 (截断预览): {reply_message[:100]}...")
                                send_result = send_group_message(TARGET_GROUP_ID, reply_message)
                                if send_result.get("status") == "ok" or send_result.get("retcode") == 0:
                                    print("  LLM 回复发送成功。")
                                else:
                                    print(f"  LLM 回复发送失败: {send_result.get('message', '未知错误')}")
                            else:
                                # Handle LLM errors (timeout, connection, API error, etc.)
                                print(f"  LLM 调用失败 ({llm_response})，发送错误提示。")
                                error_map = {
                                     "error_timeout": "请求超时了",
                                     "error_connection": "无法连接到服务",
                                     "error_api": "API返回错误",
                                     "error_empty_response": "模型没有返回有效内容",
                                     "error_unknown": "发生未知错误"
                                 }
                                error_desc = error_map.get(llm_response, "未知问题")
                                error_msg = f"抱歉，思考的时候出了点小问题 ({error_desc})。你可以稍后再试。"
                                error_reply = f"[{CURRENT_TEXT_MODEL}]: [CQ:at,qq={sender_id}] {error_msg}"
                                send_group_message(TARGET_GROUP_ID, error_reply)

                            message_processed_in_cycle = True
                            # State already updated earlier

                    # End of whitelist check else block
                # End of check if message was already processed

            # --- Final Sleep ---
            # Optional: Log end of cycle state
            # print(f"--- Cycle End --- Current State: T={last_processed_time}, ID={last_processed_msg_id} ---")
            time.sleep(CHECK_INTERVAL)

    except KeyboardInterrupt:
        print("\n接收到 Ctrl+C，正在停止...")
    except Exception as e:
        print(f"\n发生未捕获的严重错误: {e}")
        import traceback
        traceback.print_exc() # Print stack trace for debugging
    finally:
        print("正在保存最终配置...")
        save_config()
        print("配置已保存。程序退出。")