#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GPT图像生成功能
"""

import json
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from GPT_image import generate_and_download_image
    print("✅ 成功导入 generate_and_download_image")
except ImportError as e:
    print(f"❌ 无法导入 generate_and_download_image: {e}")
    sys.exit(1)

# 加载配置
try:
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    print("✅ 成功加载配置文件")
except Exception as e:
    print(f"❌ 无法加载配置文件: {e}")
    sys.exit(1)

# 获取配置参数
GPT_IMAGE_API_KEY = config.get("GPT_IMAGE_API_KEY")
GPT_IMAGE_API_URL = config.get("GPT_IMAGE_API_URL")
GPT_IMAGE_MODEL = config.get("GPT_IMAGE_MODEL", "gpt-4o-image")
GPT_IMAGE_OUTPUT_DIR = config.get("GPT_IMAGE_OUTPUT_DIR", "generated_images")

print(f"API URL: {GPT_IMAGE_API_URL}")
print(f"API Key: {GPT_IMAGE_API_KEY[:10]}..." if GPT_IMAGE_API_KEY else "未配置")
print(f"模型: {GPT_IMAGE_MODEL}")
print(f"输出目录: {GPT_IMAGE_OUTPUT_DIR}")

def test_gpt_image_generation():
    """测试GPT图像生成功能"""
    test_prompt = "画一只可爱的小猫咪在阳光下玩耍"
    
    print(f"\n🎨 开始测试图像生成...")
    print(f"提示词: {test_prompt}")
    
    try:
        # 调用图像生成函数
        image_path = generate_and_download_image(
            prompt=test_prompt,
            api_key=GPT_IMAGE_API_KEY,
            api_endpoint=GPT_IMAGE_API_URL,
            output_dir=GPT_IMAGE_OUTPUT_DIR,
            model=GPT_IMAGE_MODEL,
            verbose=True
        )
        
        if image_path:
            print(f"✅ 图像生成成功！")
            print(f"保存路径: {image_path}")
            
            # 检查文件是否存在
            if os.path.exists(image_path):
                file_size = os.path.getsize(image_path)
                print(f"文件大小: {file_size} bytes")
                
                # 生成用于QQ机器人的文件路径
                file_url = f"file:///{image_path}"
                print(f"CQ码格式: [CQ:image,file={file_url},cache=0]")
                
                return True
            else:
                print(f"❌ 文件不存在: {image_path}")
                return False
        else:
            print("❌ 图像生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== GPT图像生成功能测试 ===")
    
    # 检查必要的配置
    if not GPT_IMAGE_API_KEY:
        print("❌ GPT_IMAGE_API_KEY 未配置")
        sys.exit(1)
    
    if not GPT_IMAGE_API_URL:
        print("❌ GPT_IMAGE_API_URL 未配置")
        sys.exit(1)
    
    # 运行测试
    success = test_gpt_image_generation()
    
    if success:
        print("\n🎉 测试完成！GPT图像生成功能正常工作。")
    else:
        print("\n😞 测试失败！请检查配置和网络连接。")
        sys.exit(1)
