import requests  # Use requests for synchronous HTTP calls
import json
import webbrowser
import os
import re
import openai
from datetime import datetime

# --- OpenAI Configuration (Keep if needed for prompt optimization elsewhere) ---
# openai.api_key = 'sk-fjHxMJ39BRfq03rpV6ctkgyc20ke14dHXgsDPQ1OHzUm81Wz'
# openai.api_base = "https://api.aigogo.top/v1"

# --- DMX API Configuration ---
# DO NOT HARDCODE KEYS IN PRODUCTION CODE. Use environment variables or a config file.
# key1 = "sk-vFlXrrwpUyXeM5SjCef1UXkLFaLsRM8JK7jByBKe5ir3HS6F" # Example Key 1
# key2 = "sk-UsI4UwsYPBrZh7wYateLKiE6V2xWAovSuXYNaaz0OleRStn3" # Example Key 2
DMX_API_KEY = "sk-vFlXrrwpUyXeM5SjCef1UXkLFaLsRM8JK7jByBKe5ir3HS6F" # USE YOUR ACTUAL KEY HERE
DMX_API_HOST = "www.dmxapi.cn"
DMX_API_ENDPOINT = "/v1/images/generations"
DMX_BASE_URL = f"https://{DMX_API_HOST}"

# --- Default Request Parameters ---
DEFAULT_MODEL_NAME = "seedream-3.0"
DEFAULT_IMAGE_SIZE = "1024x1024"
DEFAULT_SAVE_FOLDER = r"C:\Users\<USER>\Desktop\seed_sync" # Define a save folder

# --- Request Headers ---
headers = {
    "Authorization": f"Bearer {DMX_API_KEY}",
    "Accept": "application/json",
    "User-Agent": "DMXAPI/1.0.0 (https://www.dmxapi.cn)",
    "Content-Type": "application/json",
}

# --- Helper Functions ---
def sanitize_filename(filename):
    """Removes invalid characters for Windows filenames."""
    invalid_chars = r'[\/:*?"<>|\n\[\]\：]' # Added square brackets
    sanitized = re.sub(invalid_chars, '', filename)
    # Limit length to avoid issues
    return sanitized[:100] # Limit filename length

def ensure_save_folder(folder_path):
    """Creates the save folder if it doesn't exist."""
    if not os.path.exists(folder_path):
        try:
            os.makedirs(folder_path)
            print(f"Created save folder: {folder_path}")
        except OSError as e:
            print(f"Error creating directory {folder_path}: {e}")
            return False
    return True

def download_image(image_url, save_folder, base_filename="image"):
    """Downloads an image from a URL and saves it."""
    if not ensure_save_folder(save_folder):
        return None # Could not create or access folder

    try:
        # Use stream=True for potentially large image files
        response = requests.get(image_url, stream=True, timeout=60) # Added timeout
        response.raise_for_status() # Raise an exception for bad status codes (4xx or 5xx)

        current_time = datetime.now().strftime("%Y%m%d%H%M%S")
        # Sanitize the base filename derived from prompt or default
        safe_base_filename = sanitize_filename(base_filename)
        save_path = os.path.join(save_folder, f"{safe_base_filename}_{current_time}.jpg")

        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192): # Download in chunks
                f.write(chunk)
        print(f"Image successfully downloaded and saved to {save_path}")
        return save_path
    except requests.exceptions.RequestException as e:
        print(f"Error downloading image from {image_url}: {e}")
    except IOError as e:
        print(f"Error saving image to {save_path}: {e}")
    except Exception as e:
        print(f"An unexpected error occurred during download: {e}")
    return None


# --- Synchronous Image Generation Function ---
def generate_single_image(prompt: str, model: str = DEFAULT_MODEL_NAME, size: str = DEFAULT_IMAGE_SIZE) -> str | None:
    """
    Generates a single image using the DMX API based on the provided prompt.

    Args:
        prompt: The text prompt to generate the image from.
        model: The model name to use (e.g., "seedream-3.0").
        size: The desired image size (e.g., "1024x1024").

    Returns:
        The URL of the generated image as a string, or None if an error occurred.
    """
    api_url = f"{DMX_BASE_URL}{DMX_API_ENDPOINT}"
    payload = json.dumps(
        {
            "prompt": prompt,
            "n": 1,  # Request exactly one image
            "model": model,
            "size": size,
        }
    )

    print(f"Requesting image generation for prompt: '{prompt}'...")
    try:
        response = requests.post(api_url, data=payload, headers=headers, timeout=120) # Add timeout
        response.raise_for_status()  # Check for HTTP errors (like 4xx or 5xx)

        response_data = response.json() # Use .json() directly

        # Check if the response contains the expected data structure
        if "data" in response_data and isinstance(response_data["data"], list) and len(response_data["data"]) > 0:
            image_info = response_data["data"][0]
            if "url" in image_info:
                image_url = image_info["url"].replace("\\u0026", "&") # Handle potential unicode escapes
                print(f"Successfully generated image URL: {image_url}")
                return image_url
            else:
                print(f"Error: 'url' key not found in image data for prompt: '{prompt}'. Response: {response_data}")
                return None
        else:
            print(f"Error: Unexpected response format or no data found for prompt: '{prompt}'. Response: {response_data}")
            return None

    except requests.exceptions.Timeout:
        print(f"Error: Request timed out for prompt: '{prompt}'")
        return None
    except requests.exceptions.RequestException as e:
        # Handles connection errors, HTTP errors, etc.
        print(f"Error during API request for prompt '{prompt}': {e}")
        # Log more details if needed, e.g., response.text for non-JSON errors
        try:
            print(f"Response status: {response.status_code}")
            print(f"Response text: {response.text}")
        except NameError: # If response object doesn't exist (e.g., connection error)
             pass
        return None
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON response for prompt '{prompt}'. Response text: {response.text}")
        return None
    except Exception as e:
        # Catch any other unexpected errors
        print(f"An unexpected error occurred for prompt '{prompt}': {e}")
        return None


# --- Example Usage ---
if __name__ == "__main__":
    # Ensure the target save directory exists before trying to save
    ensure_save_folder(DEFAULT_SAVE_FOLDER)

    # --- Example 1: Generate image and get URL ---
    prompt1 = "一只可爱的短毛猫在阳光下打盹"
    image_url = generate_single_image(prompt1)

    if image_url:
        print(f"\nGenerated image URL for '{prompt1}': {image_url}")
        # Optionally open the URL in a web browser
        # webbrowser.open(image_url)

        # --- Example 2: Generate image, get URL, and download it ---
        # Use the first few words of the prompt for a base filename
        base_filename = prompt1.split()[0] + "_" + prompt1.split()[1] if len(prompt1.split()) > 1 else "image"
        saved_path = download_image(image_url, DEFAULT_SAVE_FOLDER, base_filename=base_filename)
        if saved_path:
            print(f"Image for '{prompt1}' saved to: {saved_path}")
        else:
            print(f"Failed to download image for '{prompt1}'.")

    else:
        print(f"\nFailed to generate image for prompt: '{prompt1}'")

    # --- Example 3: Another prompt ---
    prompt2 = "赛博朋克风格的城市夜景，霓虹灯闪烁，飞行汽车穿梭"
    image_url_2 = generate_single_image(prompt2)
    if image_url_2:
        print(f"\nGenerated image URL for '{prompt2}': {image_url_2}")
        # Optional download:
        saved_path_2 = download_image(image_url_2, DEFAULT_SAVE_FOLDER, base_filename="cyberpunk_city")
        if saved_path_2:
            print(f"Image for '{prompt2}' saved to: {saved_path_2}")

    else:
         print(f"\nFailed to generate image for prompt: '{prompt2}'")
