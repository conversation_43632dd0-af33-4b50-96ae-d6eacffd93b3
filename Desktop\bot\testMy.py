import requests
import json

class TavilySearchAPI:
    def __init__(self, api_key):
        """初始化Tavily搜索API客户端"""
        self.api_key = api_key
        self.api_url = "https://api.tavily.com/search"
        
    def search(self, query, max_results=15, content_length=800, num_results=8):
        """
        执行搜索并返回整理后的结果
        
        参数:
            query: 搜索查询字符串
            max_results: 从API获取的最大结果数
            content_length: 每个结果内容的最大长度（字符）
            num_results: 最终返回的结果数
            
        返回:
            dict: 包含搜索结果和概括的字典
        """
        # 发送搜索请求
        result = self._send_search_request(query, max_results)
        if not result:
            return {"success": False, "error": "搜索请求失败"}
        
        # 整理上下文
        context = self._prepare_context(result, num_results, content_length)
        
        return {
            "success": True,
            "query": query,
            "answer": result.get("answer", "无自动生成答案"),
            "context": context,
            "results_count": len(result.get("results", []))
        }
    
    def _send_search_request(self, query, max_results):
        """发送搜索请求到Tavily API"""
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        payload = {
            "query": query,
            "search_depth": "advanced",
            "include_domains": [],
            "exclude_domains": [],
            "include_answer": True,
            "include_raw_content": True,
            "include_images": False,
            "max_results": max_results
        }
        
        try:
            response = requests.post(self.api_url, headers=headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"搜索请求出错: {e}")
            return None
    
    def _prepare_context(self, result, num_results, content_length):
        """从搜索结果中提取并整理上下文信息"""
        context = []
        
        # 添加自动生成的答案（如果有）
        if result.get("answer"):
            context.append(f"搜索自动生成的答案: {result['answer']}")
        
        # 添加搜索结果内容
        results = result.get("results", [])
        if results:
            context.append("\n搜索结果内容:")
            for i, item in enumerate(results[:num_results], 1):
                title = item.get("title", f"结果 {i}")
                url = item.get("url", "无链接")
                content = item.get("content", "无内容").replace("\n", " ")[:content_length]
                context.append(f"\n[{i}] {title}\nURL: {url}\n内容: {content}...")
        
        return "\n\n".join(context)

# 使用示例
if __name__ == "__main__":
    # 请替换为你的Tavily API密钥
    API_KEY = "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"
    
    # 初始化搜索API客户端
    search_api = TavilySearchAPI(api_key=API_KEY)
    
    # 执行搜索
    query = "斯飞"
    result = search_api.search(query)
    
    # 打印结果
    if result["success"]:
        print(f"查询: {result['query']}")
        print(f"自动概括答案: {result['answer']}")
        print(f"\n详细搜索结果:\n{result['context']}")
        print(f"\n总搜索结果数: {result['results_count']}")
    else:
        print(f"搜索失败: {result['error']}")