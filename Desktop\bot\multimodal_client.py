# multimodal_client.py
import requests
import json
import os
import base64
import mimetypes
from typing import Optional

class MultimodalClient:
    def __init__(
        self,
        api_key: str,
        api_url: str,
        model_name: str = "gemini-2.5-pro-exp-03-25", # Default model if needed
        temperature: float = 0.7,
        max_tokens: int = 4096, # Use a reasonable default
        request_timeout: int = 240
    ):
        """
        Initializes the Multimodal API client.

        Args:
            api_key: API key for authentication.
            api_url: The full URL endpoint for the chat completions API.
            model_name: The default multimodal model to use if not overridden.
            temperature: Default sampling temperature.
            max_tokens: Default maximum tokens to generate.
            request_timeout: Default request timeout in seconds.
        """
        if not api_key:
            raise ValueError("API key cannot be empty.")
        if not api_url:
            raise ValueError("API URL cannot be empty.")

        self.api_key = api_key
        self.api_url = api_url
        self.model_name = model_name # Store default model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.request_timeout = request_timeout
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        print(f"MultimodalClient initialized for model '{self.model_name}' at URL: {self.api_url}")
        print(f"  Timeout: {self.request_timeout}s, Max Tokens: {self.max_tokens}, Temp: {self.temperature}")


    def _encode_image_to_base64_uri(self, image_path: str) -> Optional[str]:
        """Encodes a local image file to a Base64 Data URI."""
        if not os.path.exists(image_path) or not os.path.isfile(image_path):
            print(f"Warning [MultimodalClient]: Image file does not exist or is not a file: '{image_path}'")
            return None
        try:
            mime_type, _ = mimetypes.guess_type(image_path)
            if not mime_type or not mime_type.startswith('image'):
                print(f"Warning [MultimodalClient]: Cannot determine a valid image MIME type for '{image_path}'. Detected: {mime_type}")
                return None

            with open(image_path, "rb") as image_file:
                binary_data = image_file.read()

            base64_encoded_data = base64.b64encode(binary_data)
            base64_string = base64_encoded_data.decode('utf-8')

            return f"data:{mime_type};base64,{base64_string}"

        except FileNotFoundError:
            # Should be caught by the initial check, but good practice
            print(f"Warning [MultimodalClient]: Image file not found during encoding: '{image_path}'")
            return None
        except Exception as e:
            print(f"Error [MultimodalClient]: Failed to encode image '{image_path}': {e}")
            return None

    def generate_response(self, prompt: str, image_path: Optional[str] = None, model_override: Optional[str] = None) -> Optional[str]:
        """
        Sends a request to the configured API endpoint, potentially including text and an image.

        Args:
            prompt: The text prompt (required). Cannot be empty or whitespace only.
            image_path: (Optional) Full path to a local image file.
            model_override: (Optional) Specify a different model for this request.

        Returns:
            The model's text response string (trimmed), or None if an error occurs
            (timeout, network error, API error, invalid response format, etc.).
            Returns "" if the API successfully returns an empty content string.
        """
        if not prompt or prompt.isspace():
            print("Error [MultimodalClient]: Prompt cannot be empty or whitespace only.")
            return None

        message_content = []
        # 1. Add text part
        message_content.append({"type": "text", "text": prompt})

        # 2. Add image part if path is valid
        image_data_uri = None
        if image_path:
            print(f"Info [MultimodalClient]: Encoding image from path: {image_path}")
            image_data_uri = self._encode_image_to_base64_uri(image_path)
            if image_data_uri:
                message_content.append({
                    "type": "image_url",
                    "image_url": {"url": image_data_uri} # Standard OpenAI format key
                })
                print("Info [MultimodalClient]: Image successfully encoded and added.")
            else:
                # Encoding failed, warning printed in _encode_image...
                print("Warning [MultimodalClient]: Image encoding failed. Sending request without image.")
                # Proceed with text-only

        request_model = model_override if model_override else self.model_name

        payload = {
            "model": request_model,
            "messages": [{"role": "user", "content": message_content}],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            # "stream": False, # Default is non-streaming
        }

        print(f"\n--- [MultimodalClient] Sending Request ---")
        print(f"Model: {request_model}")
        print(f"Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
        if image_path and image_data_uri:
            print(f"Image Included: {os.path.basename(image_path)}")
        elif image_path and not image_data_uri:
            print(f"Image Failed to Include: {os.path.basename(image_path)}")
        # Avoid printing full payload due to potentially large base64 string
        # print(f"Payload Keys: {list(payload.keys())}")
        print("------------------------------------------\n")

        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=self.request_timeout
            )
            response.raise_for_status() # Check for HTTP 4xx/5xx errors

            response_data = response.json()

            # --- Extract response content ---
            if "choices" in response_data and isinstance(response_data["choices"], list) and len(response_data["choices"]) > 0:
                choice = response_data["choices"][0]
                message = choice.get("message", {})
                finish_reason = choice.get("finish_reason", "N/A")

                content = message.get("content") # Primary field

                # Debugging info
                print(f"Info [MultimodalClient]: Response received. Finish reason: {finish_reason}")
                if 'usage' in response_data:
                     print(f"Info [MultimodalClient]: Usage: {response_data['usage']}")

                if isinstance(content, str):
                    return content.strip()  # Return even if it's ""
                else:
                    # Handle cases where content might be None or not a string unexpectedly
                     print(f"Warning [MultimodalClient]: Response message content is not a string or is missing. Content type: {type(content)}")
                     # Check for alternative field mentioned in the initial description (less common)
                     reasoning_content = message.get("reasoning_content")
                     if isinstance(reasoning_content, str):
                         print("Info [MultimodalClient]: Using fallback 'reasoning_content' field.")
                         return reasoning_content.strip()
                     else:
                         print("Error [MultimodalClient]: No valid 'content' or 'reasoning_content' found.")
                         print("Full Response Data:", json.dumps(response_data, indent=2))
                         return None

            elif "error" in response_data:
                error_info = response_data['error']
                error_msg = error_info.get('message', 'Unknown API error') if isinstance(error_info, dict) else str(error_info)
                print(f"Error [MultimodalClient]: API returned an error: {error_msg}")
                print("Full Response Data:", json.dumps(response_data, indent=2))
                return None
            else:
                print("Error [MultimodalClient]: Unexpected API response format. Missing 'choices' or 'error'.")
                print("Full Response Data:", json.dumps(response_data, indent=2))
                return None

        except requests.exceptions.Timeout:
            print(f"Error [MultimodalClient]: Request timed out after {self.request_timeout} seconds.")
            return None
        except requests.exceptions.RequestException as e:
            print(f"Error [MultimodalClient]: Network or request error: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Response Status Code: {e.response.status_code}")
                try:
                    print(f"Response Body: {e.response.text}") # Log raw text
                except Exception as read_err:
                    print(f"Could not read response body: {read_err}")
            return None
        except json.JSONDecodeError:
            print(f"Error [MultimodalClient]: Failed to decode JSON response from server.")
            if 'response' in locals():
                 print("Raw Response Text:", response.text)
            return None
        except Exception as e:
            # Catch-all for other unexpected errors during the process
            import traceback
            print(f"Error [MultimodalClient]: An unexpected error occurred: {e}")
            print(traceback.format_exc())
            return None

# Example usage (optional, for testing this file directly)
if __name__ == "__main__":
    print("Testing MultimodalClient...")
    # Load credentials securely, e.g., from environment variables or a Git-ignored file
    try:
        test_api_key = "sk-D2deUoCT3BnVVrMw7EIqvsJKLemfPvd65kj2gBwLhTUCGoBf"
        test_api_url = "http://121.62.28.36:3000/v1/chat/completions"
        test_model = "gemini-2.5-pro-exp-03-25" # Or your specific test model

        # Create a dummy image file for testing
        dummy_image_path = "test_image.png"
        try:
            from PIL import Image
            img = Image.new('RGB', (60, 30), color = 'red')
            img.save(dummy_image_path)
            print(f"Created dummy image: {dummy_image_path}")
        except ImportError:
            print("PIL/Pillow not installed. Cannot create dummy image. Create 'test_image.png' manually.")
            # You might need to create a small test image manually named test_image.png

        if not os.path.exists(dummy_image_path):
             print(f"ERROR: Dummy image '{dummy_image_path}' not found. Skipping image test.")
             dummy_image_path = None


        client = MultimodalClient(
            api_key=test_api_key,
            api_url=test_api_url,
            model_name=test_model,
            request_timeout=60
        )

        # Test 1: Text only
        print("\n--- Test 1: Text Only ---")
        prompt_text = "Explain the concept of large language models in simple terms."
        response_text = client.generate_response(prompt=prompt_text)
        if response_text is not None:
            print("\nModel Response (Text Only):")
            print(response_text)
        else:
            print("\nFailed to get text-only response.")

        # Test 2: Text and Image
        if dummy_image_path and os.path.exists(dummy_image_path):
            print("\n--- Test 2: Text and Image ---")
            prompt_image = "Describe this image."
            response_image = client.generate_response(prompt=prompt_image, image_path=dummy_image_path)
            if response_image is not None:
                print("\nModel Response (Text + Image):")
                print(response_image)
            else:
                print("\nFailed to get text+image response.")
        else:
            print("\n--- Skipping Test 2: Text and Image (No dummy image) ---")

        # Clean up dummy image
        if dummy_image_path and os.path.exists(dummy_image_path):
             try:
                 os.remove(dummy_image_path)
                 print(f"\nRemoved dummy image: {dummy_image_path}")
             except OSError as e:
                 print(f"Error removing dummy image: {e}")

    except KeyError:
        print("\nERROR: Please set MULTIMODAL_TEST_API_KEY and MULTIMODAL_TEST_API_URL environment variables to test.")
    except ValueError as e:
         print(f"\nERROR during client initialization: {e}")
    except Exception as e:
        print(f"\nAn unexpected error occurred during testing: {e}")
