import os
import requests
import json
import re
from urllib.parse import urlparse
from typing import Optional # For type hinting

def generate_and_download_image(
    prompt: str,
    api_key: str,
    api_endpoint: str,
    output_dir: Optional[str] = None,
    model: str = "gpt-4o-image",
    verbose: bool = False
) -> Optional[str]:
    """
    Generates an image using a specified API endpoint based on a prompt,
    extracts the download link from the streaming response, downloads the image,
    and saves it.

    Args:
        prompt (str): The text prompt describing the desired image.
        api_key (str): Your API Key for authentication.
        api_endpoint (str): The API endpoint URL for chat completions.
        output_dir (Optional[str]): The directory to save the image.
                                     If None, defaults to the user's Desktop.
        model (str): The model name to use for image generation
                     (e.g., "gpt-4o-image"). Defaults to "gpt-4o-image".
        verbose (bool): If True, prints status messages during execution.
                        Defaults to True.

    Returns:
        Optional[str]: The full path to the saved image file if successful,
                       otherwise None.
    """
    if verbose:
        print(f"--- Starting Image Generation for: '{prompt}' ---")

    # --- 1. Prepare Request ---
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    messages = [{"role": "user", "content": prompt}]
    data = {
        "model": model,
        "messages": messages,
        "stream": True,
    }

    final_content = ""
    image_url = None

    # --- 2. Send API Request and Handle Stream ---
    try:
        if verbose:
            print(f"Sending request to {api_endpoint} with model {model}...")
        response = requests.post(api_endpoint, headers=headers, data=json.dumps(data), stream=True, timeout=180) # Added timeout
        response.raise_for_status() # Check for HTTP errors like 4xx/5xx

        if verbose:
            print("Receiving streaming response...")
            print("API Response: ", end="", flush=True)

        for raw_line in response.iter_lines(decode_unicode=True):
            if not raw_line:
                continue
            if raw_line.startswith("data: "):
                content = raw_line[len("data: "):].strip()
                if content == "[DONE]":
                    break
                try:
                    chunk = json.loads(content)
                    choices = chunk.get("choices", [])
                    if choices:
                        delta = choices[0].get("delta", {})
                        content_piece = delta.get("content")
                        if content_piece:
                            if verbose:
                                print(content_piece, end="", flush=True)
                            final_content += content_piece
                except json.JSONDecodeError:
                    if verbose:
                        print(f"\nWarning: Could not decode JSON chunk: {content}")
                    continue
        if verbose:
           print("\nStream finished.") # New line after streaming output

    except requests.exceptions.Timeout:
        if verbose:
            print(f"\n❌ Error: Request timed out after 180 seconds.")
        return None
    except requests.exceptions.RequestException as e:
        if verbose:
            print(f"\n❌ Error during API request: {e}")
            if hasattr(e, "response") and e.response is not None:
                try:
                    error_details = e.response.text
                    print(f"    Status Code: {e.response.status_code}")
                    print(f"    Response Body: {error_details[:500]}{'...' if len(error_details) > 500 else ''}") # Print first 500 chars
                except Exception as read_err:
                    print(f"    Failed to read error response details: {read_err}")
        return None
    except Exception as e: # Catch other potential errors during streaming/parsing
        if verbose:
            print(f"\n❌ An unexpected error occurred during streaming: {e}")
        return None


    # --- 3. Extract Download Link ---
    if not final_content:
        if verbose:
            print("⚠️ API response was empty.")
        return None

    # Regex to find Markdown link like [任意文字](URL) or ![任意文字](URL)
    # Making it more general just in case the text isn't exactly "点击下载"
    link_match = re.search(r"!?\[.*?\]\((https?://[^\)]+)\)", final_content)

    if link_match:
        image_url = link_match.group(1)
        if verbose:
            print(f"✅ Image URL extracted: {image_url}")
    else:
        if verbose:
            print("⚠️ Could not find a download URL in the API response.")
            # print("\n--- Full API Response ---")
            # print(final_content)
            # print("-----------------------\n")
        return None

    # --- 4. Download Image ---
    try:
        if verbose:
            print(f"Downloading image from {image_url}...")
        # Some servers require a common User-Agent
        img_headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
        img_response = requests.get(image_url, headers=img_headers, stream=True, allow_redirects=True, timeout=60) # Added timeout, allow redirects
        img_response.raise_for_status() # Check download request

        # --- 5. Determine Save Path and Save File ---
        # Default to Desktop if output_dir is not specified
        if output_dir is None:
            output_dir = os.path.join(os.path.expanduser("~"), "Desktop")

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Generate filename
        parsed_url = urlparse(image_url)
        base_filename = os.path.basename(parsed_url.path)
        # Basic sanitization and default name
        if not base_filename or "." not in base_filename:
             # Try to get content-type for extension
             content_type = img_response.headers.get('content-type')
             ext = '.png' # default
             if content_type:
                 if 'jpeg' in content_type or 'jpg' in content_type:
                     ext = '.jpg'
                 elif 'png' in content_type:
                     ext = '.png'
                 elif 'webp' in content_type:
                     ext = '.webp'
                 elif 'gif' in content_type:
                      ext = '.gif'
             base_filename = f"generated_image_{os.urandom(4).hex()}{ext}" # Add random hex to avoid clashes
        else:
             # Simple sanitization: remove potentially problematic characters
             base_filename = re.sub(r'[\\/*?:"<>|]', "", base_filename)

        # Combine directory and filename
        full_save_path = os.path.join(output_dir, base_filename)

        if verbose:
            print(f"Saving image to: {full_save_path}")

        # Save the image chunk by chunk
        with open(full_save_path, "wb") as img_file:
            for chunk in img_response.iter_content(chunk_size=8192):
                img_file.write(chunk)

        if verbose:
            print(f"✅ Image successfully saved: {full_save_path}")
        return full_save_path # Return the path on success

    except requests.exceptions.Timeout:
        if verbose:
            print(f"❌ Error: Image download timed out after 60 seconds.")
        return None
    except requests.exceptions.RequestException as img_err:
        if verbose:
            print(f"❌ Error downloading image: {img_err}")
        return None
    except IOError as io_err:
        if verbose:
            print(f"❌ Error saving image file: {io_err}")
        return None
    except Exception as e: # Catch other potential errors during download/save
        if verbose:
            print(f"❌ An unexpected error occurred during download/saving: {e}")
        return None

# === Example Usage ===
if __name__ == "__main__":
    # --- 请替换为你的实际信息 ---
    MY_API_KEY = "sk-fjHxMJ39BRfq03rpV6ctkgyc20ke14dHXgsDPQ1OHzUm81Wz"  # 强烈建议使用环境变量或配置文件管理密钥
    MY_API_ENDPOINT = "https://api.aigogo.top/v1/chat/completions"    # 你的 API Endpoint
    # --- ---

    if "sk-fjH" in MY_API_KEY: # Simple check for placeholder
         print("⚠️ 请在代码中替换 'MY_API_KEY' 和 'MY_API_ENDPOINT' 为你的实际信息！")
         # exit() # You might want to uncomment this exit in production

    # --- 定义图片描述 ---
    image_prompt = "生成一张照片：一个快乐的柯基犬在公园的草地上奔跑，阳光明媚"

    # --- 调用函数生成并下载图片 (保存到桌面) ---
    saved_image_path = generate_and_download_image(
        prompt=image_prompt,
        api_key=MY_API_KEY,
        api_endpoint=MY_API_ENDPOINT,
        # output_dir=None, # Defaults to Desktop
        # model="gpt-4o-image", # Defaults to this model
        # verbose=True # Defaults to True
    )

    if saved_image_path:
        print(f"\n🎉🎉🎉 图片生成和下载完成! 文件保存在: {saved_image_path}")
    else:
        print(f"\n😥 图片生成或下载失败。请查看上面的错误信息。")

    print("\n--- 再次调用示例 (指定输出目录和文件名) ---")
    # --- 调用函数生成并下载图片 (保存到指定目录) ---
    custom_prompt = "画一幅水彩画：宁静的湖面倒映着雪山和松树"
    custom_dir = "generated_images" # 会自动创建此目录

    # 确保目录存在（或者让函数内部处理）
    # os.makedirs(custom_dir, exist_ok=True)

    saved_image_path_2 = generate_and_download_image(
        prompt=custom_prompt,
        api_key=MY_API_KEY,
        api_endpoint=MY_API_ENDPOINT,
        output_dir=custom_dir,
        verbose=True # Be explicit for clarity
    )

    if saved_image_path_2:
        print(f"\n🎉🎉🎉 第二张图片成功保存: {saved_image_path_2}")
    else:
        print(f"\n😥 第二张图片生成或下载失败。")